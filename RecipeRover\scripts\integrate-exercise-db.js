#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to integrate Free Exercise DB data with RecipeRover
 * Downloads exercise data and images, merges with existing database
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const EXERCISE_DB_URL = 'https://raw.githubusercontent.com/yuhonas/free-exercise-db/main/dist/exercises.json';
const IMAGE_BASE_URL = 'https://raw.githubusercontent.com/yuhonas/free-exercise-db/main/exercises/';
const OUTPUT_DIR = path.join(__dirname, '../data/exercises');
const IMAGES_DIR = path.join(OUTPUT_DIR, 'images');

// Ensure directories exist
async function ensureDirectories() {
  await fs.mkdir(OUTPUT_DIR, { recursive: true });
  await fs.mkdir(IMAGES_DIR, { recursive: true });
}

// Download exercise data
async function downloadExerciseData() {
  console.log('📥 Downloading exercise database...');
  
  try {
    const response = await fetch(EXERCISE_DB_URL);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const exercises = await response.json();
    console.log(`✅ Downloaded ${exercises.length} exercises`);
    
    // Save raw data
    await fs.writeFile(
      path.join(OUTPUT_DIR, 'free-exercise-db.json'),
      JSON.stringify(exercises, null, 2)
    );
    
    return exercises;
  } catch (error) {
    console.error('❌ Failed to download exercise data:', error);
    throw error;
  }
}

// Transform exercise data to RecipeRover format
function transformExerciseData(exercises) {
  console.log('🔄 Transforming exercise data...');
  
  return exercises.map(exercise => ({
    id: exercise.id,
    name: exercise.name,
    description: `${exercise.name} - ${exercise.category} exercise`,
    instructions: exercise.instructions || [],
    primaryMuscleGroups: exercise.primaryMuscles || [],
    secondaryMuscleGroups: exercise.secondaryMuscles || [],
    difficulty: exercise.level || 'intermediate',
    equipment: exercise.equipment ? [exercise.equipment] : ['bodyweight'],
    category: exercise.category || 'strength',
    safetyTips: [],
    videoUrl: null,
    imageUrl: null,
    // Enhanced fields from Free Exercise DB
    force: exercise.force,
    mechanic: exercise.mechanic,
    images: exercise.images || [],
    level: exercise.level,
    // Generate image URLs
    imageUrls: (exercise.images || []).map(imagePath => 
      `${IMAGE_BASE_URL}${imagePath}`
    )
  }));
}

// Download exercise images (optional - for local caching)
async function downloadExerciseImages(exercises, limit = 50) {
  console.log(`🖼️  Downloading exercise images (limit: ${limit})...`);
  
  let downloaded = 0;
  
  for (const exercise of exercises.slice(0, limit)) {
    if (!exercise.images || exercise.images.length === 0) continue;
    
    const exerciseDir = path.join(IMAGES_DIR, exercise.id);
    await fs.mkdir(exerciseDir, { recursive: true });
    
    for (const imagePath of exercise.images) {
      try {
        const imageUrl = `${IMAGE_BASE_URL}${imagePath}`;
        const response = await fetch(imageUrl);
        
        if (response.ok) {
          const buffer = await response.arrayBuffer();
          const fileName = path.basename(imagePath);
          await fs.writeFile(
            path.join(exerciseDir, fileName),
            Buffer.from(buffer)
          );
          downloaded++;
        }
      } catch (error) {
        console.warn(`⚠️  Failed to download image for ${exercise.name}:`, error.message);
      }
    }
    
    // Rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`✅ Downloaded ${downloaded} exercise images`);
}

// Generate SQL migration script
async function generateMigrationScript(transformedExercises) {
  console.log('📝 Generating database migration script...');
  
  const migrationSQL = `
-- Migration: Add Free Exercise DB data
-- Generated: ${new Date().toISOString()}

-- Add new columns to exercises table
ALTER TABLE exercises ADD COLUMN IF NOT EXISTS force TEXT;
ALTER TABLE exercises ADD COLUMN IF NOT EXISTS mechanic TEXT;
ALTER TABLE exercises ADD COLUMN IF NOT EXISTS level TEXT;
ALTER TABLE exercises ADD COLUMN IF NOT EXISTS images TEXT; -- JSON array

-- Insert/Update exercises
${transformedExercises.map(exercise => `
INSERT OR REPLACE INTO exercises (
  id, name, description, instructions, primaryMuscleGroups, 
  secondaryMuscleGroups, difficulty, equipment, category, 
  safetyTips, videoUrl, imageUrl, force, mechanic, level, images
) VALUES (
  '${exercise.id}',
  '${exercise.name.replace(/'/g, "''")}',
  '${exercise.description.replace(/'/g, "''")}',
  '${JSON.stringify(exercise.instructions).replace(/'/g, "''")}',
  '${JSON.stringify(exercise.primaryMuscleGroups)}',
  '${JSON.stringify(exercise.secondaryMuscleGroups)}',
  '${exercise.difficulty}',
  '${JSON.stringify(exercise.equipment)}',
  '${exercise.category}',
  '${JSON.stringify(exercise.safetyTips)}',
  ${exercise.videoUrl ? `'${exercise.videoUrl}'` : 'NULL'},
  ${exercise.imageUrls.length > 0 ? `'${exercise.imageUrls[0]}'` : 'NULL'},
  ${exercise.force ? `'${exercise.force}'` : 'NULL'},
  ${exercise.mechanic ? `'${exercise.mechanic}'` : 'NULL'},
  '${exercise.level}',
  '${JSON.stringify(exercise.imageUrls)}'
);`).join('\n')}

-- Update statistics
SELECT 'Migration completed. Total exercises: ' || COUNT(*) FROM exercises;
`;

  await fs.writeFile(
    path.join(OUTPUT_DIR, 'migration.sql'),
    migrationSQL
  );
  
  console.log('✅ Migration script generated');
}

// Generate TypeScript types
async function generateTypeDefinitions() {
  console.log('📝 Generating TypeScript definitions...');
  
  const typeDefinitions = `
// Generated exercise types from Free Exercise DB integration
// Generated: ${new Date().toISOString()}

export interface EnhancedExercise {
  id: string;
  name: string;
  description: string;
  instructions: string[];
  primaryMuscleGroups: string[];
  secondaryMuscleGroups: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  equipment: string[];
  category: 'strength' | 'cardio' | 'flexibility' | 'plyometric' | 'powerlifting' | 'strongman' | 'stretching';
  safetyTips: string[];
  videoUrl?: string | null;
  imageUrl?: string | null;
  
  // Enhanced fields from Free Exercise DB
  force?: 'pull' | 'push' | 'static' | null;
  mechanic?: 'compound' | 'isolation' | null;
  level: 'beginner' | 'intermediate' | 'expert';
  images: string[];
  imageUrls: string[];
}

export interface ExerciseSearchFilters {
  category?: string;
  difficulty?: string;
  equipment?: string;
  muscleGroup?: string;
  force?: string;
  mechanic?: string;
}
`;

  await fs.writeFile(
    path.join(__dirname, '../shared/enhanced-exercise-types.ts'),
    typeDefinitions
  );
  
  console.log('✅ TypeScript definitions generated');
}

// Main execution
async function main() {
  try {
    console.log('🚀 Starting Free Exercise DB integration...\n');
    
    await ensureDirectories();
    
    // Download and transform data
    const rawExercises = await downloadExerciseData();
    const transformedExercises = transformExerciseData(rawExercises);
    
    // Save transformed data
    await fs.writeFile(
      path.join(OUTPUT_DIR, 'enhanced-exercises.json'),
      JSON.stringify(transformedExercises, null, 2)
    );
    
    // Generate migration and types
    await generateMigrationScript(transformedExercises);
    await generateTypeDefinitions();
    
    // Optionally download images (uncomment to enable)
    // await downloadExerciseImages(rawExercises, 100);
    
    console.log('\n✅ Integration completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Review generated files in data/exercises/');
    console.log('2. Run the migration script to update your database');
    console.log('3. Update your exercise components to use enhanced data');
    console.log('4. Uncomment image download if you want local caching');
    
  } catch (error) {
    console.error('\n❌ Integration failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main as integrateExerciseDB };
