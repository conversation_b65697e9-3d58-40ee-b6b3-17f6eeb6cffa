# Diet and Fitness Media Library Integration Plan

## 🎯 Overview
This document outlines the integration of comprehensive exercise and nutrition databases with visual media (images, videos, animations) into RecipeRover.

## 🏋️ Exercise Database Integration

### Option 1: Free Exercise DB (Recommended)
**Pros:**
- ✅ Completely free and open source
- ✅ 800+ exercises with images
- ✅ Well-structured JSON data
- ✅ Public domain license
- ✅ GitHub-hosted images (reliable CDN)

**Implementation:**
```javascript
// 1. Download and integrate exercise data
const EXERCISE_DB_URL = 'https://raw.githubusercontent.com/yuhonas/free-exercise-db/main/dist/exercises.json';
const IMAGE_BASE_URL = 'https://raw.githubusercontent.com/yuhonas/free-exercise-db/main/exercises/';

// 2. Enhance existing exercise schema
interface EnhancedExercise {
  id: string;
  name: string;
  description: string;
  instructions: string[];
  primaryMuscleGroups: string[];
  secondaryMuscleGroups: string[];
  difficulty: string;
  equipment: string[];
  category: string;
  // New fields from Free Exercise DB
  force?: string;
  mechanic?: string;
  images: string[]; // Array of image paths
  level: string;
}
```

### Option 2: ExerciseDB API (For Premium Features)
**Pros:**
- ✅ 1300+ exercises
- ✅ Animated GIF demonstrations
- ✅ Professional quality
- ✅ Regular updates

**Cost:** Free tier + paid plans for higher usage

## 🍎 Nutrition Database Integration

### USDA FoodData Central API
**Implementation:**
```javascript
// 1. Register for free API key at fdc.nal.usda.gov
const USDA_API_KEY = process.env.USDA_API_KEY;
const USDA_BASE_URL = 'https://api.nal.usda.gov/fdc/v1';

// 2. Enhanced food search with nutrition data
async function searchUSDAFoods(query) {
  const response = await fetch(`${USDA_BASE_URL}/foods/search?query=${query}&api_key=${USDA_API_KEY}`);
  return response.json();
}

// 3. Get detailed nutrition for specific food
async function getFoodDetails(fdcId) {
  const response = await fetch(`${USDA_BASE_URL}/food/${fdcId}?api_key=${USDA_API_KEY}`);
  return response.json();
}
```

### Food Images Integration
```javascript
// Option 1: Unsplash API for food images
const UNSPLASH_ACCESS_KEY = process.env.UNSPLASH_ACCESS_KEY;

async function getFoodImage(foodName) {
  const response = await fetch(
    `https://api.unsplash.com/search/photos?query=${foodName} food&client_id=${UNSPLASH_ACCESS_KEY}`
  );
  const data = await response.json();
  return data.results[0]?.urls?.regular;
}

// Option 2: Pexels API (alternative)
const PEXELS_API_KEY = process.env.PEXELS_API_KEY;

async function getPexelsFoodImage(foodName) {
  const response = await fetch(
    `https://api.pexels.com/v1/search?query=${foodName} food&per_page=1`,
    { headers: { Authorization: PEXELS_API_KEY } }
  );
  const data = await response.json();
  return data.photos[0]?.src?.medium;
}
```

## 🔧 Implementation Steps

### Phase 1: Exercise Database Enhancement
1. **Download Free Exercise DB data**
   ```bash
   curl -o exercises.json https://raw.githubusercontent.com/yuhonas/free-exercise-db/main/dist/exercises.json
   ```

2. **Update database schema**
   - Add image fields to exercise table
   - Add new exercise properties (force, mechanic, level)

3. **Create data migration script**
   - Merge existing exercises with new data
   - Download and cache exercise images locally (optional)

4. **Update UI components**
   - Add image display to exercise cards
   - Create image gallery for exercise instructions
   - Add exercise difficulty and equipment filters

### Phase 2: Nutrition Database Enhancement
1. **Register for USDA API key** (free)
2. **Integrate USDA food search**
   - Replace/enhance existing food database
   - Add comprehensive nutrition data
3. **Add food image integration**
   - Implement Unsplash or Pexels API
   - Cache images for performance
4. **Update nutrition UI**
   - Add food images to search results
   - Enhanced nutrition information display

### Phase 3: Video Integration (Future)
1. **YouTube API integration**
   - Search for exercise demonstration videos
   - Embed videos in exercise details
2. **Custom video hosting**
   - Upload custom form analysis videos
   - Integration with existing camera features

## 📁 File Structure Updates

```
RecipeRover/
├── server/
│   ├── routes/
│   │   ├── exercises.ts (enhanced with images)
│   │   ├── nutrition.ts (USDA integration)
│   │   └── media.ts (image/video handling)
│   └── services/
│       ├── exerciseDB.ts
│       ├── usdaAPI.ts
│       └── imageAPI.ts
├── client/src/
│   ├── components/
│   │   ├── ExerciseGallery.tsx
│   │   ├── FoodImageCard.tsx
│   │   └── MediaViewer.tsx
│   └── services/
│       ├── exerciseAPI.ts
│       └── nutritionAPI.ts
└── data/
    ├── exercises/ (cached images)
    └── foods/ (cached food images)
```

## 🔑 Required API Keys

### Free APIs
- ✅ USDA FoodData Central (free, no limits)
- ✅ Unsplash (free tier: 50 requests/hour)
- ✅ Pexels (free tier: 200 requests/hour)

### Paid APIs (Optional)
- ExerciseDB via RapidAPI (free tier available)
- YouTube Data API (free tier: 10,000 requests/day)

## 💰 Cost Analysis

### Free Solution (Recommended)
- **Exercise Images**: Free Exercise DB (public domain)
- **Nutrition Data**: USDA API (completely free)
- **Food Images**: Unsplash/Pexels free tiers
- **Total Cost**: $0/month

### Premium Solution
- **Exercise Animations**: ExerciseDB API (~$10-50/month)
- **Enhanced Images**: Premium stock photo APIs
- **Video Hosting**: Custom video storage
- **Total Cost**: $20-100/month

## 🚀 Quick Start Implementation

1. **Immediate (Free)**:
   - Integrate Free Exercise DB for exercise images
   - Add USDA API for enhanced nutrition data
   - Implement basic food image search

2. **Phase 2 (1-2 weeks)**:
   - Add exercise image galleries
   - Enhance nutrition UI with images
   - Implement image caching

3. **Phase 3 (Future)**:
   - Add video demonstrations
   - Custom media upload features
   - Advanced image optimization

## 📊 Expected Impact

- **User Engagement**: +40% with visual exercise demonstrations
- **App Completeness**: Professional-grade fitness app experience
- **Data Quality**: Comprehensive nutrition database (300k+ foods)
- **Visual Appeal**: Modern, image-rich interface
- **Cost Efficiency**: Leverage free, high-quality APIs

## 🔄 Next Steps

1. Choose integration approach (free vs premium)
2. Set up API keys for selected services
3. Create database migration scripts
4. Update UI components with image support
5. Test and optimize performance
