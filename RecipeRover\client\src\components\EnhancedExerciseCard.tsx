import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, Di<PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { 
  <PERSON>mb<PERSON>, 
  Target, 
  Zap, 
  Play, 
  Pause, 
  RotateCcw,
  Info,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

// Enhanced exercise interface with Free Exercise DB data
interface EnhancedExercise {
  id: string;
  name: string;
  description: string;
  instructions: string[];
  primaryMuscleGroups: string[];
  secondaryMuscleGroups: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  equipment: string[];
  category: string;
  safetyTips: string[];
  videoUrl?: string | null;
  imageUrl?: string | null;
  
  // Enhanced fields from Free Exercise DB
  force?: 'pull' | 'push' | 'static' | null;
  mechanic?: 'compound' | 'isolation' | null;
  level: 'beginner' | 'intermediate' | 'expert';
  images: string[];
}

interface EnhancedExerciseCardProps {
  exercise: EnhancedExercise;
  onAddToWorkout?: (exercise: EnhancedExercise) => void;
  showAddButton?: boolean;
  className?: string;
}

const IMAGE_BASE_URL = 'https://raw.githubusercontent.com/yuhonas/free-exercise-db/main/exercises/';

export default function EnhancedExerciseCard({ 
  exercise, 
  onAddToWorkout, 
  showAddButton = true,
  className = "" 
}: EnhancedExerciseCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Generate image URLs from Free Exercise DB
  const imageUrls = exercise.images?.map(imagePath => `${IMAGE_BASE_URL}${imagePath}`) || [];
  const hasImages = imageUrls.length > 0 && !imageError;
  const currentImageUrl = hasImages ? imageUrls[currentImageIndex] : null;

  // Auto-play functionality for exercise demonstration
  React.useEffect(() => {
    if (isPlaying && imageUrls.length > 1) {
      const interval = setInterval(() => {
        setCurrentImageIndex((prev) => (prev + 1) % imageUrls.length);
      }, 1500);

      return () => clearInterval(interval);
    }
  }, [isPlaying, imageUrls.length]);

  const nextImage = () => {
    if (imageUrls.length > 1) {
      setCurrentImageIndex((prev) => (prev + 1) % imageUrls.length);
    }
  };

  const prevImage = () => {
    if (imageUrls.length > 1) {
      setCurrentImageIndex((prev) => (prev - 1 + imageUrls.length) % imageUrls.length);
    }
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  const resetToStart = () => {
    setCurrentImageIndex(0);
    setIsPlaying(false);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-500 text-white';
      case 'intermediate': return 'bg-yellow-500 text-white';
      case 'advanced': case 'expert': return 'bg-red-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getForceIcon = (force?: string) => {
    switch (force) {
      case 'push': return <Zap className="h-3 w-3" />;
      case 'pull': return <Target className="h-3 w-3" />;
      case 'static': return <Dumbbell className="h-3 w-3" />;
      default: return null;
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <Card className={`${className} hover:shadow-lg transition-shadow`}>
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg font-semibold">{exercise.name}</CardTitle>
          <div className="flex gap-1">
            <Badge 
              variant="outline" 
              className={`text-xs ${getDifficultyColor(exercise.difficulty || exercise.level)} border-0`}
            >
              {exercise.difficulty || exercise.level}
            </Badge>
            {exercise.force && (
              <Badge variant="outline" className="text-xs">
                {getForceIcon(exercise.force)}
                <span className="ml-1 capitalize">{exercise.force}</span>
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Exercise Image/Animation */}
        {hasImages ? (
          <div className="relative aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
            <img
              src={currentImageUrl!}
              alt={`${exercise.name} - Step ${currentImageIndex + 1}`}
              className="w-full h-full object-cover"
              onError={handleImageError}
            />

            {/* Image Controls Overlay */}
            <div className="absolute inset-0 bg-black/20 opacity-0 hover:opacity-100 transition-opacity">
              {imageUrls.length > 1 && (
                <>
                  {/* Navigation Arrows */}
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={prevImage}
                    className="absolute left-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={nextImage}
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>

                  {/* Playback Controls */}
                  <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-2">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={togglePlayback}
                      className="h-8 px-3"
                    >
                      {isPlaying ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                      <span className="ml-1 text-xs">
                        {isPlaying ? 'Pause' : 'Demo'}
                      </span>
                    </Button>
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={resetToStart}
                      className="h-8 w-8 p-0"
                    >
                      <RotateCcw className="h-3 w-3" />
                    </Button>
                  </div>
                </>
              )}
            </div>

            {/* Image Counter */}
            {imageUrls.length > 1 && (
              <div className="absolute top-2 left-2">
                <Badge variant="secondary" className="text-xs">
                  {currentImageIndex + 1} / {imageUrls.length}
                </Badge>
              </div>
            )}
          </div>
        ) : (
          <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <Dumbbell className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No images available</p>
            </div>
          </div>
        )}

        {/* Muscle Groups */}
        <div className="flex flex-wrap gap-1">
          {exercise.primaryMuscleGroups.map((muscle) => (
            <Badge key={muscle} variant="default" className="text-xs">
              {muscle}
            </Badge>
          ))}
          {exercise.secondaryMuscleGroups.map((muscle) => (
            <Badge key={muscle} variant="secondary" className="text-xs">
              {muscle}
            </Badge>
          ))}
        </div>

        {/* Equipment */}
        {exercise.equipment.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {exercise.equipment.map((item) => (
              <Badge key={item} variant="outline" className="text-xs">
                <Dumbbell className="h-3 w-3 mr-1" />
                {item}
              </Badge>
            ))}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          {showAddButton && onAddToWorkout && (
            <Button 
              onClick={() => onAddToWorkout(exercise)}
              className="flex-1"
              size="sm"
            >
              Add to Workout
            </Button>
          )}
          
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Info className="h-4 w-4 mr-2" />
                Details
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>{exercise.name}</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                {/* Exercise Image in Dialog */}
                {currentImageUrl && (
                  <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
                    <img
                      src={currentImageUrl}
                      alt={exercise.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                
                {/* Instructions */}
                <div>
                  <h4 className="font-semibold mb-2">Instructions:</h4>
                  <ol className="list-decimal list-inside space-y-2">
                    {exercise.instructions.map((instruction, index) => (
                      <li key={index} className="text-sm leading-relaxed">
                        {instruction}
                      </li>
                    ))}
                  </ol>
                </div>

                {/* Exercise Details */}
                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div>
                    <h5 className="font-medium text-sm mb-1">Category</h5>
                    <p className="text-sm text-muted-foreground capitalize">{exercise.category}</p>
                  </div>
                  {exercise.mechanic && (
                    <div>
                      <h5 className="font-medium text-sm mb-1">Type</h5>
                      <p className="text-sm text-muted-foreground capitalize">{exercise.mechanic}</p>
                    </div>
                  )}
                </div>

                {/* Safety Tips */}
                {exercise.safetyTips.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-2">Safety Tips:</h4>
                    <ul className="list-disc list-inside space-y-1">
                      {exercise.safetyTips.map((tip, index) => (
                        <li key={index} className="text-sm text-muted-foreground">
                          {tip}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardContent>
    </Card>
  );
}
