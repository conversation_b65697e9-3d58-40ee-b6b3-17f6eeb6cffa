# 🍎 Nutrition APIs Setup Guide

## 🎯 Overview
This guide will help you integrate free nutrition APIs to add comprehensive food data and images to your RecipeRover application.

## 🔑 Free API Keys Required

### 1. USDA FoodData Central (FREE - Unlimited)
**What it provides:** 300,000+ foods with complete nutrition data
- **Website:** https://fdc.nal.usda.gov/api-key-signup
- **Cost:** Completely FREE with unlimited requests
- **Data:** Calories, macros, vitamins, minerals for all foods
- **Setup:**
  1. Visit the signup page
  2. Fill out the simple form
  3. Get your API key instantly
  4. Add to `.env`: `USDA_API_KEY=your_key_here`

### 2. Unsplash Images (FREE - 50 requests/hour)
**What it provides:** High-quality food photography
- **Website:** https://unsplash.com/developers
- **Cost:** FREE tier with 50 requests/hour
- **Setup:**
  1. Create an Unsplash account
  2. Go to "Your Apps" and create a new application
  3. Get your "Access Key"
  4. Add to `.env`: `UNSPLASH_ACCESS_KEY=your_key_here`

### 3. Pexels Images (FREE - 200 requests/hour)
**What it provides:** Alternative food photography source
- **Website:** https://www.pexels.com/api/
- **Cost:** FREE tier with 200 requests/hour
- **Setup:**
  1. Create a Pexels account
  2. Request API access
  3. Get your API key
  4. Add to `.env`: `PEXELS_API_KEY=your_key_here`

## 🚀 Quick Setup

### Step 1: Get Your API Keys
Visit the links above and get your free API keys (takes 5 minutes total).

### Step 2: Update Your .env File
Add these lines to your `.env` file:
```bash
# Nutrition APIs (all FREE)
USDA_API_KEY=your_usda_api_key_here
UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here
PEXELS_API_KEY=your_pexels_api_key_here
```

### Step 3: Test the Integration
```bash
# Start your server
npm run dev

# Test USDA nutrition data
curl "http://localhost:5000/api/nutrition/foods?q=chicken"

# Test food images (after implementing the API routes)
curl "http://localhost:5000/api/nutrition/foods/search-enhanced?q=apple"
```

## 📊 What You Get

### USDA Nutrition Database
- ✅ **300,000+ foods** with complete nutrition data
- ✅ **Comprehensive macros:** calories, protein, carbs, fat, fiber
- ✅ **Micronutrients:** vitamins, minerals, sodium
- ✅ **Brand foods:** packaged foods with UPC codes
- ✅ **Restaurant foods:** chain restaurant menu items
- ✅ **Completely free** with unlimited requests

### Food Images
- ✅ **High-quality photos** for visual food logging
- ✅ **Professional photography** from Unsplash/Pexels
- ✅ **Automatic food recognition** and image matching
- ✅ **Free tiers** sufficient for most applications

## 🔧 Implementation Examples

### Basic USDA Food Search
```javascript
// Search for foods
const response = await fetch(
  `https://api.nal.usda.gov/fdc/v1/foods/search?query=chicken&api_key=${USDA_API_KEY}`
);
const data = await response.json();

// Get detailed nutrition for a specific food
const foodResponse = await fetch(
  `https://api.nal.usda.gov/fdc/v1/food/123456?api_key=${USDA_API_KEY}`
);
const foodData = await foodResponse.json();
```

### Food Image Search
```javascript
// Get food image from Unsplash
const imageResponse = await fetch(
  `https://api.unsplash.com/search/photos?query=chicken food&client_id=${UNSPLASH_ACCESS_KEY}`
);
const imageData = await imageResponse.json();
const imageUrl = imageData.results[0]?.urls?.regular;
```

## 💰 Cost Breakdown

| Service | Free Tier | Paid Plans | Recommended |
|---------|-----------|------------|-------------|
| USDA API | Unlimited FREE | N/A | ✅ Perfect |
| Unsplash | 50 requests/hour | $99/month for more | ✅ Sufficient |
| Pexels | 200 requests/hour | Contact for more | ✅ Great backup |

**Total monthly cost for most apps: $0** 🎉

## 🔄 Rate Limiting Strategy

### For Production Apps:
1. **Cache food data** locally after first search
2. **Cache images** to avoid repeated API calls
3. **Implement fallback images** for foods without photos
4. **Use both Unsplash and Pexels** for better coverage

### Example Caching:
```javascript
// Cache nutrition data
const cachedFood = localStorage.getItem(`food-${foodId}`);
if (cachedFood) {
  return JSON.parse(cachedFood);
}

// Cache images
const cachedImage = localStorage.getItem(`image-${foodName}`);
if (cachedImage) {
  return cachedImage;
}
```

## 🚨 Important Notes

1. **USDA API Key:** Keep it secure but it's not as sensitive as payment APIs
2. **Image APIs:** Respect rate limits to avoid temporary blocks
3. **Attribution:** Consider crediting photographers for Unsplash/Pexels images
4. **Fallbacks:** Always have placeholder images for foods without photos

## 🎯 Next Steps

1. ✅ Get your free API keys (5 minutes)
2. ✅ Add them to your `.env` file
3. ✅ Restart your development server
4. ✅ Test the basic API calls
5. ✅ Implement enhanced food search in your UI
6. ✅ Add food images to your nutrition logging

## 🆘 Troubleshooting

### Common Issues:
- **"Invalid API key":** Double-check your `.env` file and restart server
- **"Rate limit exceeded":** Wait an hour or implement caching
- **"No images found":** Try different search terms or use fallback images
- **"CORS errors":** Make sure API calls are from your server, not client

### Support:
- USDA API: https://fdc.nal.usda.gov/help.html
- Unsplash API: https://unsplash.com/documentation
- Pexels API: https://www.pexels.com/api/documentation/

---

🎉 **You're all set!** Your RecipeRover app now has access to professional-grade nutrition data and food imagery - all for FREE!
