import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  ChevronLeft, 
  ChevronRight, 
  Play, 
  Pause, 
  RotateCcw,
  Maximize2,
  Info,
  Dumbbell,
  Target,
  Zap
} from 'lucide-react';

interface ExerciseImage {
  url: string;
  alt: string;
  caption?: string;
}

interface EnhancedExercise {
  id: string;
  name: string;
  description: string;
  instructions: string[];
  primaryMuscleGroups: string[];
  secondaryMuscleGroups: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  equipment: string[];
  category: string;
  force?: 'pull' | 'push' | 'static';
  mechanic?: 'compound' | 'isolation';
  level: 'beginner' | 'intermediate' | 'expert';
  images: string[];
  imageUrls: string[];
}

interface ExerciseImageGalleryProps {
  exercise: EnhancedExercise;
  className?: string;
  showInstructions?: boolean;
}

const IMAGE_BASE_URL = 'https://raw.githubusercontent.com/yuhonas/free-exercise-db/main/exercises/';

export default function ExerciseImageGallery({ 
  exercise, 
  className = "",
  showInstructions = true 
}: ExerciseImageGalleryProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showFullscreen, setShowFullscreen] = useState(false);

  // Generate image URLs if not provided
  const imageUrls = exercise.imageUrls?.length > 0 
    ? exercise.imageUrls 
    : exercise.images?.map(imagePath => `${IMAGE_BASE_URL}${imagePath}`) || [];

  const hasImages = imageUrls.length > 0;
  const currentImageUrl = hasImages ? imageUrls[currentImageIndex] : null;

  // Auto-play functionality for exercise demonstration
  React.useEffect(() => {
    if (isPlaying && imageUrls.length > 1) {
      const interval = setInterval(() => {
        setCurrentImageIndex((prev) => (prev + 1) % imageUrls.length);
      }, 1500); // Change image every 1.5 seconds

      return () => clearInterval(interval);
    }
  }, [isPlaying, imageUrls.length]);

  const nextImage = () => {
    if (imageUrls.length > 1) {
      setCurrentImageIndex((prev) => (prev + 1) % imageUrls.length);
    }
  };

  const prevImage = () => {
    if (imageUrls.length > 1) {
      setCurrentImageIndex((prev) => (prev - 1 + imageUrls.length) % imageUrls.length);
    }
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  const resetToStart = () => {
    setCurrentImageIndex(0);
    setIsPlaying(false);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-500';
      case 'intermediate': return 'bg-yellow-500';
      case 'advanced': case 'expert': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getForceIcon = (force?: string) => {
    switch (force) {
      case 'push': return <Zap className="h-3 w-3" />;
      case 'pull': return <Target className="h-3 w-3" />;
      case 'static': return <Dumbbell className="h-3 w-3" />;
      default: return null;
    }
  };

  if (!hasImages) {
    return (
      <Card className={`${className} border-dashed`}>
        <CardContent className="flex items-center justify-center h-48 text-muted-foreground">
          <div className="text-center">
            <Dumbbell className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No images available</p>
            <p className="text-sm">Exercise: {exercise.name}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      <Card>
        <CardContent className="p-0">
          {/* Image Display */}
          <div className="relative aspect-video bg-gray-100 dark:bg-gray-800 rounded-t-lg overflow-hidden">
            {currentImageUrl && (
              <img
                src={currentImageUrl}
                alt={`${exercise.name} - Step ${currentImageIndex + 1}`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Fallback for broken images
                  const target = e.target as HTMLImageElement;
                  target.src = '/placeholder-exercise.png';
                }}
              />
            )}

            {/* Image Controls Overlay */}
            <div className="absolute inset-0 bg-black/20 opacity-0 hover:opacity-100 transition-opacity">
              <div className="absolute top-2 right-2 flex gap-2">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => setShowFullscreen(true)}
                  className="h-8 w-8 p-0"
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
              </div>

              {imageUrls.length > 1 && (
                <>
                  {/* Navigation Arrows */}
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={prevImage}
                    className="absolute left-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={nextImage}
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>

                  {/* Playback Controls */}
                  <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-2">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={togglePlayback}
                      className="h-8 px-3"
                    >
                      {isPlaying ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                      <span className="ml-1 text-xs">
                        {isPlaying ? 'Pause' : 'Demo'}
                      </span>
                    </Button>
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={resetToStart}
                      className="h-8 w-8 p-0"
                    >
                      <RotateCcw className="h-3 w-3" />
                    </Button>
                  </div>
                </>
              )}
            </div>

            {/* Image Counter */}
            {imageUrls.length > 1 && (
              <div className="absolute top-2 left-2">
                <Badge variant="secondary" className="text-xs">
                  {currentImageIndex + 1} / {imageUrls.length}
                </Badge>
              </div>
            )}
          </div>

          {/* Exercise Info */}
          <div className="p-4">
            <div className="flex items-start justify-between mb-2">
              <h3 className="font-semibold text-lg">{exercise.name}</h3>
              <div className="flex gap-1">
                <Badge 
                  variant="outline" 
                  className={`text-xs ${getDifficultyColor(exercise.difficulty)} text-white border-0`}
                >
                  {exercise.difficulty}
                </Badge>
                {exercise.force && (
                  <Badge variant="outline" className="text-xs">
                    {getForceIcon(exercise.force)}
                    <span className="ml-1 capitalize">{exercise.force}</span>
                  </Badge>
                )}
              </div>
            </div>

            {/* Muscle Groups */}
            <div className="flex flex-wrap gap-1 mb-3">
              {exercise.primaryMuscleGroups.map((muscle) => (
                <Badge key={muscle} variant="default" className="text-xs">
                  {muscle}
                </Badge>
              ))}
              {exercise.secondaryMuscleGroups.map((muscle) => (
                <Badge key={muscle} variant="secondary" className="text-xs">
                  {muscle}
                </Badge>
              ))}
            </div>

            {/* Equipment */}
            {exercise.equipment.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {exercise.equipment.map((item) => (
                  <Badge key={item} variant="outline" className="text-xs">
                    <Dumbbell className="h-3 w-3 mr-1" />
                    {item}
                  </Badge>
                ))}
              </div>
            )}

            {/* Instructions Preview */}
            {showInstructions && exercise.instructions.length > 0 && (
              <div className="mt-3">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" className="w-full">
                      <Info className="h-4 w-4 mr-2" />
                      View Instructions
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>{exercise.name} - Instructions</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      {/* Exercise Images in Dialog */}
                      {currentImageUrl && (
                        <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
                          <img
                            src={currentImageUrl}
                            alt={exercise.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}
                      
                      {/* Detailed Instructions */}
                      <div>
                        <h4 className="font-semibold mb-2">Step-by-step Instructions:</h4>
                        <ol className="list-decimal list-inside space-y-2">
                          {exercise.instructions.map((instruction, index) => (
                            <li key={index} className="text-sm leading-relaxed">
                              {instruction}
                            </li>
                          ))}
                        </ol>
                      </div>

                      {/* Exercise Details */}
                      <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                        <div>
                          <h5 className="font-medium text-sm mb-1">Category</h5>
                          <p className="text-sm text-muted-foreground capitalize">{exercise.category}</p>
                        </div>
                        {exercise.mechanic && (
                          <div>
                            <h5 className="font-medium text-sm mb-1">Type</h5>
                            <p className="text-sm text-muted-foreground capitalize">{exercise.mechanic}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Fullscreen Dialog */}
      <Dialog open={showFullscreen} onOpenChange={setShowFullscreen}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <div className="relative aspect-video bg-black">
            {currentImageUrl && (
              <img
                src={currentImageUrl}
                alt={`${exercise.name} - Step ${currentImageIndex + 1}`}
                className="w-full h-full object-contain"
              />
            )}
            
            {/* Fullscreen Controls */}
            {imageUrls.length > 1 && (
              <div className="absolute inset-0">
                <Button
                  size="lg"
                  variant="secondary"
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2"
                >
                  <ChevronLeft className="h-6 w-6" />
                </Button>
                <Button
                  size="lg"
                  variant="secondary"
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2"
                >
                  <ChevronRight className="h-6 w-6" />
                </Button>
                
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
                  <Button
                    variant="secondary"
                    onClick={togglePlayback}
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    <span className="ml-2">{isPlaying ? 'Pause Demo' : 'Play Demo'}</span>
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={resetToStart}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
